import { Box, Stack, Typography, ListItemButton } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { fToNow } from 'src/utils/format-time';
import { Iconify } from 'src/components/iconify';
import { NotificationItemProps, getTranslatedNotification } from 'src/_mock/_notifications';

// ----------------------------------------------------------------------

export function NotificationItem({ notification }: { notification: NotificationItemProps }) {
  const { t } = useTranslation();
  const { title, description, isUnRead, createdAt, time, type } = notification;

  // Get translated title and description based on notification type
  const translatedText = getTranslatedNotification(type);
  const displayTitle = translatedText.title || title;
  const displayDescription = translatedText.description || description;

  return (
    <ListItemButton
      onClick={() => {
        // In a real app, you would navigate to the notification detail page
        console.log(`Navigating to notification ${notification.id} details`);
      }}
      sx={{
        py: 2,
        px: 2.5,
        borderBottom: '1px solid #EEEEEE',
        ...(isUnRead && {
          bgcolor: 'rgba(255, 107, 53, 0.08)',
          borderRadius: 0,
        }),
      }}
    >
      <Stack direction="row" spacing={2} alignItems="flex-start">
        <Box
          sx={{
            width: 32,
            height: 32,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            borderRadius: '50%',
            bgcolor: isUnRead ? '#FEE4DC' : 'white',
            border: '1px solid #EEEEEE',
            color: isUnRead ? '#FF6B35' : '#757575',
            flexShrink: 0,
          }}
        >
          <Iconify icon="mdi:bell-outline" width={18} height={18} />
        </Box>

        <Stack sx={{ flexGrow: 1 }}>
          <Typography
            variant="subtitle2"
            sx={{
              fontWeight: 600,
              mb: 0.5,
            }}
          >
            {displayTitle}
          </Typography>

          <Typography
            variant="body2"
            sx={{
              color: 'text.secondary',
              mb: 0.5,
              lineHeight: '1.5',
            }}
          >
            {displayDescription}
          </Typography>

          <Typography
            variant="caption"
            sx={{
              color: 'text.disabled',
            }}
          >
            {time || fToNow(createdAt)}
          </Typography>

          {isUnRead && (
            <Typography
              variant="caption"
              component="div"
              onClick={(e: React.MouseEvent) => {
                e.stopPropagation();
                // In a real app, you would call an API to mark the notification as read
                console.log(`Marking notification ${notification.id} as read`);
              }}
              sx={{
                color: '#FF6B35',
                fontSize: '0.75rem',
                mt: 0.5,
                cursor: 'pointer',
                width: 'fit-content',
                '&:hover': {
                  textDecoration: 'underline',
                },
              }}
            >
              {t('common.markAllAsRead')}
            </Typography>
          )}
        </Stack>
      </Stack>
    </ListItemButton>
  );
}

// ----------------------------------------------------------------------
