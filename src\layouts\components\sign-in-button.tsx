import type { ButtonProps } from '@mui/material/Button';

import Button from '@mui/material/Button';
import { useTranslation } from 'react-i18next';

import { RouterLink } from 'src/routes/components';

import { CONFIG } from 'src/config-global';

// ----------------------------------------------------------------------

export function SignInButton({ sx, ...other }: ButtonProps) {
  const { t } = useTranslation();

  return (
    <Button
      component={RouterLink}
      href={CONFIG.auth.redirectPath}
      variant="outlined"
      sx={sx}
      {...other}
    >
      {t('pages.auth.signIn.title')}
    </Button>
  );
}
