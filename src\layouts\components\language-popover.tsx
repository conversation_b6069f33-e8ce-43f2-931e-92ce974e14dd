import type { IconButtonProps } from '@mui/material/IconButton';
import { useTranslation } from 'react-i18next';

import { m } from 'framer-motion';
import { useState, useCallback } from 'react';

import MenuList from '@mui/material/MenuList';
import MenuItem from '@mui/material/MenuItem';
import IconButton from '@mui/material/IconButton';

import { varHover } from 'src/components/animate';
import { FlagIcon } from 'src/components/iconify';
import { usePopover, CustomPopover } from 'src/components/custom-popover';
import { useSettingsContext } from 'src/components/settings';

// ----------------------------------------------------------------------

export type LanguagePopoverProps = IconButtonProps & {
  data?: {
    value: string;
    label: string;
    countryCode: string;
  }[];
};

export function LanguagePopover({ data = [], sx, ...other }: LanguagePopoverProps) {
  const popover = usePopover();

  const { i18n } = useTranslation();
  const currentLng = localStorage.getItem('i18nextLng') || 'en';

  const [locale, setLocale] = useState<string>(currentLng);
  const currentLang = data.find((lang) => lang.value === locale);
  const settings = useSettingsContext();

  const changeLanguage = (lng: string) => i18n.changeLanguage(lng);
  const handleChangeLang = useCallback(
    (newLang: string) => {
      if (newLang === 'ar') {
        settings.onUpdateField('direction', 'rtl');
        changeLanguage(newLang);
      } else {
        settings.onUpdateField('direction', 'ltr');
        changeLanguage(newLang);
      }
      setLocale(newLang);
      localStorage.setItem('i18nextLng', newLang);
      popover.onClose();
    },
    [popover, settings]
  );

  return (
    <>
      <IconButton
        component={m.button}
        whileTap="tap"
        whileHover="hover"
        variants={varHover(1.05)}
        onClick={popover.onOpen}
        sx={{
          p: 0,
          width: 40,
          height: 40,
          ...(popover.open && { bgcolor: 'action.selected' }),
          ...sx,
        }}
        {...other}
      >
        <FlagIcon code={currentLang?.countryCode} />
      </IconButton>

      <CustomPopover open={popover.open} anchorEl={popover.anchorEl} onClose={popover.onClose}>
        <MenuList sx={{ width: 160, minHeight: 72 }}>
          {data?.map((option) => (
            <MenuItem
              key={option.value}
              selected={option.value === currentLang?.value}
              onClick={() => handleChangeLang(option.value)}
            >
              <FlagIcon code={option.countryCode} />
              {option.label}
            </MenuItem>
          ))}
        </MenuList>
      </CustomPopover>
    </>
  );
}
