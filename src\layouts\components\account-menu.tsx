import type { IconButtonProps } from '@mui/material/IconButton';

import { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';

import Box from '@mui/material/Box';
import Menu from '@mui/material/Menu';
import Avatar from '@mui/material/Avatar';
import MenuItem from '@mui/material/MenuItem';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';

import { paths } from 'src/routes/paths';
import { useRouter } from 'src/routes/hooks';

import { Iconify } from 'src/components/iconify';
import { useMockedUser } from 'src/auth/hooks';

import { AccountButton } from './account-button';

// ----------------------------------------------------------------------

export type AccountMenuProps = IconButtonProps & {
  data?: {
    label: string;
    href: string;
    icon?: React.ReactNode;
    info?: React.ReactNode;
  }[];
};

export function AccountMenu({ data = [], sx, ...other }: AccountMenuProps) {
  const router = useRouter();
  const { user } = useMockedUser();
  const { t } = useTranslation();

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleOpenMenu = useCallback((event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  }, []);

  const handleCloseMenu = useCallback(() => {
    setAnchorEl(null);
  }, []);

  const handleClickItem = useCallback(
    (path: string) => {
      handleCloseMenu();
      router.push(path);
    },
    [handleCloseMenu, router]
  );

  return (
    <>
      <AccountButton
        open={open}
        onClick={handleOpenMenu}
        photoURL={user?.photoURL}
        displayName={user?.displayName}
        sx={sx}
        {...other}
      />

      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleCloseMenu}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        PaperProps={{
          sx: {
            width: 220,
            p: 1,
            mt: 1.5,
            boxShadow: (theme) => theme.customShadows.dropdown,
          },
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', p: 1.5 }}>
          <Avatar
            src={user?.photoURL}
            alt={user?.displayName}
            sx={{ width: 40, height: 40, mr: 1.5 }}
          />
          <Box>
            <Typography variant="subtitle1" noWrap>
              {user?.displayName}
            </Typography>
            <Typography variant="body2" sx={{ color: 'text.secondary' }} noWrap>
              {user?.email}
            </Typography>
          </Box>
        </Box>

        <MenuItem onClick={() => handleClickItem(paths.dashboard.profile.root)} sx={{ py: 1.5 }}>
          <Iconify icon="mdi:account-outline" sx={{ mr: 2, width: 20, height: 20 }} />
          {t('navigation.profile.myProfile')}
        </MenuItem>

        <MenuItem
          onClick={() => handleClickItem(paths.dashboard.profile.knowledgeBase)}
          sx={{ py: 1.5 }}
        >
          <Iconify icon="mdi:book-open-outline" sx={{ mr: 2, width: 20, height: 20 }} />
          {t('navigation.profile.knowledgeBase')}
        </MenuItem>

        <MenuItem
          onClick={() => handleClickItem(paths.dashboard.profile.settings)}
          sx={{ py: 1.5 }}
        >
          <Iconify icon="mdi:cog-outline" sx={{ mr: 2, width: 20, height: 20 }} />
          {t('navigation.profile.settings')}
        </MenuItem>
      </Menu>
    </>
  );
}
